import speech_recognition as sr

def listen():
    """
    Listen to microphone input and return recognized text
    """
    recognizer = sr.Recognizer()
    with sr.Microphone() as source:
        print("🎙️ Listening for your command...")
        recognizer.adjust_for_ambient_noise(source, duration=0.5)  # reduce noise
        try:
            audio = recognizer.listen(source, timeout=5, phrase_time_limit=7)
        except sr.WaitTimeoutError:
            print("⏳ Listening timed out (no speech detected).")
            return ""
    try:
        text = recognizer.recognize_google(audio).lower()
        print(f"👂 You said: {text}")
        return text
    except sr.UnknownValueError:
        print("🤔 Could not understand audio.")
        return ""
    except sr.RequestError:
        print("❌ Could not request results from speech recognition service.")
        return ""
