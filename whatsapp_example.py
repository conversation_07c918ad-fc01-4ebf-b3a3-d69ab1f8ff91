#!/usr/bin/env python3
"""
WhatsApp Integration Example for Raya AI Assistant
This script demonstrates how to use WhatsApp features with Raya.
"""

from raya_core.whatsapp_handler import WhatsA<PERSON><PERSON>andler
from raya_core import brain
import time

def demo_simple_message():
    """Demo sending a simple WhatsApp message"""
    print("\n🔹 Demo 1: Simple Message Sending")
    print("-" * 40)
    
    # Note: Replace with actual phone number (with country code)
    phone_number = input("Enter phone number (with country code, e.g., +1234567890): ")
    message = input("Enter message to send: ")
    
    if phone_number and message:
        print(f"📤 Sending message to {phone_number}...")
        
        # Using simple method (pywhatkit)
        from raya_core.whatsapp_handler import send_whatsapp_message
        success = send_whatsapp_message(phone_number, message)
        
        if success:
            print("✅ Message sent successfully!")
        else:
            print("❌ Failed to send message")
    else:
        print("❌ Phone number and message are required")

def demo_whatsapp_web_automation():
    """Demo WhatsApp Web automation"""
    print("\n🔹 Demo 2: WhatsApp Web Automation")
    print("-" * 40)
    
    handler = WhatsAppHandler()
    
    print("🌐 Setting up WhatsApp Web...")
    if handler.login_whatsapp_web():
        print("✅ Connected to WhatsApp Web!")
        
        # Demo sending message to contact
        contact_name = input("Enter contact name: ")
        message = input("Enter message: ")
        
        if contact_name and message:
            success = handler.send_message_advanced(contact_name, message)
            if success:
                print("✅ Message sent via WhatsApp Web!")
            else:
                print("❌ Failed to send message via WhatsApp Web")
        
        # Demo reading messages
        print("\n📨 Reading latest messages...")
        messages = handler.get_latest_messages(3)
        if messages:
            for i, msg in enumerate(messages, 1):
                sender = "You" if not msg['incoming'] else "Contact"
                print(f"Message {i}: [{msg['timestamp']}] {sender}: {msg['text']}")
        else:
            print("No messages found")
        
        # Keep connection open for a bit
        input("\nPress Enter to close WhatsApp Web connection...")
        handler.close()
    else:
        print("❌ Failed to connect to WhatsApp Web")

def demo_ai_integration():
    """Demo AI integration with WhatsApp"""
    print("\n🔹 Demo 3: AI Integration")
    print("-" * 40)
    
    # Simulate voice commands
    test_commands = [
        "setup whatsapp",
        "send whatsapp message to John saying Hello from Raya AI",
        "read whatsapp messages",
        "start monitoring whatsapp"
    ]
    
    print("🤖 Testing AI brain with WhatsApp commands...")
    
    for command in test_commands:
        print(f"\n🎤 Voice Command: '{command}'")
        response = brain.process(command)
        print(f"🗣️  Raya Response: {response}")
        time.sleep(1)

def demo_message_monitoring():
    """Demo message monitoring with AI responses"""
    print("\n🔹 Demo 4: Message Monitoring with AI")
    print("-" * 40)
    
    handler = WhatsAppHandler()
    
    if handler.login_whatsapp_web():
        print("✅ Connected to WhatsApp Web!")
        print("👀 Starting message monitoring...")
        print("Send yourself a WhatsApp message to test!")
        
        def ai_message_handler(message):
            """Handle incoming messages with AI"""
            print(f"\n📨 New message received: {message['text']}")
            
            # Generate AI response
            ai_response = brain.ask_gpt(
                f"Someone sent me this WhatsApp message: '{message['text']}'. "
                f"Generate a brief, friendly response (max 50 words)."
            )
            
            print(f"🤖 AI Generated Response: {ai_response}")
            
            # Ask user if they want to send the AI response
            send_response = input("Send this AI response? (y/n): ").lower().strip()
            if send_response == 'y':
                # Note: You'd need to implement auto-reply here
                print("📤 AI response would be sent automatically")
            else:
                print("🚫 AI response not sent")
        
        # Start monitoring
        handler.monitor_messages(ai_message_handler)
        
        print("Monitoring for 30 seconds... Send a message to test!")
        time.sleep(30)
        
        handler.stop_monitoring()
        handler.close()
        print("🛑 Monitoring stopped")
    else:
        print("❌ Failed to connect to WhatsApp Web")

def main():
    """Main demo function"""
    print("🤖 Raya AI Assistant - WhatsApp Integration Demo")
    print("=" * 50)
    
    demos = {
        "1": ("Simple Message Sending", demo_simple_message),
        "2": ("WhatsApp Web Automation", demo_whatsapp_web_automation),
        "3": ("AI Integration Test", demo_ai_integration),
        "4": ("Message Monitoring with AI", demo_message_monitoring)
    }
    
    while True:
        print("\n📋 Available Demos:")
        for key, (name, _) in demos.items():
            print(f"  {key}. {name}")
        print("  q. Quit")
        
        choice = input("\nSelect demo (1-4 or q): ").strip().lower()
        
        if choice == 'q':
            print("👋 Goodbye!")
            break
        elif choice in demos:
            name, demo_func = demos[choice]
            try:
                demo_func()
            except KeyboardInterrupt:
                print("\n🛑 Demo interrupted by user")
            except Exception as e:
                print(f"\n❌ Demo error: {e}")
        else:
            print("❌ Invalid choice. Please select 1-4 or q.")

if __name__ == "__main__":
    main()
