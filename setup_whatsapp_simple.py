#!/usr/bin/env python3
"""
Simple WhatsApp Setup Script for Raya AI Assistant
This script sets up WhatsApp integration using pywhatkit (no Selenium required)
"""

import os
import sys
import subprocess

def install_dependencies():
    """Install required dependencies for simple WhatsApp integration"""
    print("📦 Installing WhatsApp dependencies (simple mode)...")
    
    dependencies = [
        "pywhatkit",
        "pyautogui",
        "pillow"  # Required for pyautogui
    ]
    
    for dep in dependencies:
        try:
            print(f"Installing {dep}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"✅ {dep} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {dep}: {e}")
            return False
    
    return True

def test_pywhatkit():
    """Test pywhatkit installation"""
    print("\n🧪 Testing pywhatkit...")
    
    try:
        import pywhatkit as pwk
        print("✅ pywhatkit imported successfully")
        
        # Test basic functionality
        print("✅ pywhatkit is ready to use")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing pywhatkit: {e}")
        return False

def test_simple_whatsapp():
    """Test simple WhatsApp handler"""
    print("\n🧪 Testing simple WhatsApp handler...")
    
    try:
        from raya_core.whatsapp_simple import SimpleWhatsAppHandler
        
        handler = SimpleWhatsAppHandler()
        print("✅ Simple WhatsApp handler created successfully")
        
        # Test info retrieval
        info = handler.get_whatsapp_info()
        print("✅ WhatsApp info retrieved successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please make sure the raya_core module is in the correct location.")
        return False
    except Exception as e:
        print(f"❌ Error testing simple WhatsApp handler: {e}")
        return False

def print_usage_instructions():
    """Print instructions for using simple WhatsApp features"""
    print("\n" + "="*60)
    print("🎉 Simple WhatsApp Integration Setup Complete!")
    print("="*60)
    print("\n📋 How to use WhatsApp features with Raya (Simple Mode):")
    
    print("\n1. 🔗 Setup WhatsApp:")
    print("   Say: 'Setup WhatsApp'")
    print("   - No browser setup required!")
    
    print("\n2. 📤 Send Messages:")
    print("   Say: 'Send WhatsApp message to +1234567890 saying Hello!'")
    print("   - Use full phone numbers with country code")
    print("   - Message will open in your default browser")
    
    print("\n3. 📤 Send Group Messages:")
    print("   Say: 'Send WhatsApp group message to Family saying Hello everyone!'")
    print("   - Use exact group name as it appears in WhatsApp")
    
    print("\n4. 🌐 Open Chat:")
    print("   Say: 'Open WhatsApp chat with +1234567890'")
    print("   - Opens WhatsApp Web with specific contact")
    
    print("\n📱 Example Commands:")
    print("   • 'Send WhatsApp message to +1234567890 saying How are you?'")
    print("   • 'Send WhatsApp group message to Work Team saying Meeting at 3 PM'")
    print("   • 'Open WhatsApp chat with +1234567890'")
    
    print("\n⚠️  Simple Mode Limitations:")
    print("   • Cannot read incoming messages automatically")
    print("   • Cannot monitor messages in real-time")
    print("   • Requires manual browser interaction")
    print("   • Each message opens a new browser tab")
    
    print("\n✨ Simple Mode Benefits:")
    print("   • No complex setup required")
    print("   • No Chrome WebDriver issues")
    print("   • Works on any system with a browser")
    print("   • Lightweight and reliable")
    
    print("\n🔧 Advanced Mode:")
    print("   If you want message reading and monitoring features,")
    print("   you can try the advanced setup with: python setup_whatsapp.py")
    print("   (Requires Chrome WebDriver)")
    
    print("\n🚀 Ready to start! Run: python main.py")
    print("="*60)

def create_contact_list_example():
    """Create an example contact list file"""
    contact_file = "contacts.txt"
    
    if not os.path.exists(contact_file):
        example_contacts = """# Raya AI Assistant - Contact List
# Format: Name = Phone Number
# Example:
# John = +1234567890
# Mom = +1987654321
# Work = +1555123456

# Add your contacts below:
"""
        
        try:
            with open(contact_file, 'w') as f:
                f.write(example_contacts)
            print(f"✅ Created example contact list: {contact_file}")
            print("   You can add your contacts to this file for easier voice commands")
        except Exception as e:
            print(f"❌ Failed to create contact list: {e}")

def main():
    """Main setup function"""
    print("🤖 Raya AI Assistant - Simple WhatsApp Integration Setup")
    print("="*55)
    
    # Step 1: Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies. Please check your internet connection and try again.")
        return False
    
    # Step 2: Test pywhatkit
    if not test_pywhatkit():
        print("❌ pywhatkit test failed. Please check the error messages above.")
        return False
    
    # Step 3: Test simple WhatsApp handler
    if not test_simple_whatsapp():
        print("❌ Simple WhatsApp handler test failed. Please check the error messages above.")
        return False
    
    # Step 4: Create contact list example
    create_contact_list_example()
    
    # Step 5: Print usage instructions
    print_usage_instructions()
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Setup failed. Please resolve the errors above and try again.")
        sys.exit(1)
    else:
        print("\n✅ Simple WhatsApp setup completed successfully!")
        print("💡 This setup avoids Chrome WebDriver issues by using browser-based messaging.")
