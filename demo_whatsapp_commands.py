#!/usr/bin/env python3
"""
Demo script showing WhatsApp commands for Raya AI Assistant
This script demonstrates how to use WhatsApp features without voice input
"""

from raya_core import brain

def demo_whatsapp_commands():
    """Demo WhatsApp commands"""
    print("🤖 Raya AI Assistant - WhatsApp Commands Demo")
    print("=" * 50)
    
    # List of demo commands
    commands = [
        {
            "command": "setup whatsapp",
            "description": "Initialize WhatsApp integration"
        },
        {
            "command": "send whatsapp message to +1234567890 saying Hello from Raya AI!",
            "description": "Send a message to a phone number"
        },
        {
            "command": "send whatsapp group message to Family saying Dinner is ready!",
            "description": "Send a message to a WhatsApp group"
        },
        {
            "command": "open whatsapp chat with +1234567890",
            "description": "Open WhatsApp chat with a contact"
        },
        {
            "command": "read whatsapp messages",
            "description": "Try to read messages (not available in simple mode)"
        },
        {
            "command": "start monitoring whatsapp",
            "description": "Try to start monitoring (not available in simple mode)"
        }
    ]
    
    print("\n📋 Available WhatsApp Commands:")
    print("-" * 30)
    
    for i, cmd_info in enumerate(commands, 1):
        print(f"\n{i}. {cmd_info['description']}")
        print(f"   Command: '{cmd_info['command']}'")
    
    print("\n" + "=" * 50)
    print("🎮 Interactive Demo")
    print("=" * 50)
    
    while True:
        print("\nOptions:")
        print("1-6: Test specific command")
        print("c: Test custom command")
        print("q: Quit")
        
        choice = input("\nEnter your choice: ").strip().lower()
        
        if choice == 'q':
            print("👋 Demo ended!")
            break
        elif choice == 'c':
            custom_command = input("Enter your WhatsApp command: ").strip()
            if custom_command:
                test_command(custom_command)
        elif choice.isdigit() and 1 <= int(choice) <= len(commands):
            cmd_info = commands[int(choice) - 1]
            test_command(cmd_info['command'])
        else:
            print("❌ Invalid choice. Please try again.")

def test_command(command):
    """Test a specific command"""
    print(f"\n🎤 Testing command: '{command}'")
    print("-" * 40)
    
    try:
        response = brain.process(command)
        print(f"🗣️ Raya Response: {response}")
        
        # Special handling for send message commands
        if "send whatsapp message" in command.lower():
            print("\n💡 Note: If this was a real phone number, WhatsApp Web would open")
            print("   and the message would be sent automatically!")
        elif "open whatsapp chat" in command.lower():
            print("\n💡 Note: WhatsApp Web should have opened with the chat!")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("-" * 40)

def show_usage_examples():
    """Show practical usage examples"""
    print("\n📱 Practical WhatsApp Usage Examples:")
    print("=" * 40)
    
    examples = [
        {
            "scenario": "Send a quick message to a friend",
            "voice_command": "Send WhatsApp message to +1234567890 saying Hey, are you free for lunch?",
            "what_happens": "Opens WhatsApp Web and sends the message automatically"
        },
        {
            "scenario": "Message a family group",
            "voice_command": "Send WhatsApp group message to Family saying I'll be home late tonight",
            "what_happens": "Opens WhatsApp Web and sends message to the Family group"
        },
        {
            "scenario": "Open a chat to continue conversation",
            "voice_command": "Open WhatsApp chat with +1234567890",
            "what_happens": "Opens WhatsApp Web with that contact's chat ready"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['scenario']}")
        print(f"   Say: '{example['voice_command']}'")
        print(f"   Result: {example['what_happens']}")
    
    print("\n⚠️ Important Notes:")
    print("• Use full phone numbers with country code (e.g., +1234567890)")
    print("• Group names must match exactly as they appear in WhatsApp")
    print("• Keep your browser open while using WhatsApp features")
    print("• First message to a new contact may require manual verification")

def main():
    """Main demo function"""
    print("🚀 Starting WhatsApp Demo...")
    
    # Show usage examples first
    show_usage_examples()
    
    # Run interactive demo
    demo_whatsapp_commands()

if __name__ == "__main__":
    main()
