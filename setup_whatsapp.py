#!/usr/bin/env python3
"""
WhatsApp Setup Script for Raya AI Assistant
This script helps you set up WhatsApp integration with your Raya AI assistant.
"""

import os
import sys
import subprocess

def install_dependencies():
    """Install required dependencies for WhatsApp integration"""
    print("📦 Installing WhatsApp dependencies...")
    
    dependencies = [
        "pywhatkit",
        "selenium", 
        "webdriver-manager"
    ]
    
    for dep in dependencies:
        try:
            print(f"Installing {dep}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"✅ {dep} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {dep}: {e}")
            return False
    
    return True

def setup_chrome_driver():
    """Setup Chrome driver for WhatsApp Web automation"""
    print("\n🌐 Setting up Chrome WebDriver...")
    
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        
        # Download and setup ChromeDriver
        service = Service(ChromeDriverManager().install())
        print("✅ Chrome WebDriver setup complete")
        return True
        
    except Exception as e:
        print(f"❌ Error setting up Chrome WebDriver: {e}")
        print("Please make sure Google Chrome is installed on your system.")
        return False

def create_whatsapp_session_dir():
    """Create directory for WhatsApp session data"""
    session_dir = "./whatsapp_session"
    if not os.path.exists(session_dir):
        os.makedirs(session_dir)
        print(f"✅ Created WhatsApp session directory: {session_dir}")
    else:
        print(f"📁 WhatsApp session directory already exists: {session_dir}")

def test_whatsapp_connection():
    """Test WhatsApp Web connection"""
    print("\n🧪 Testing WhatsApp connection...")
    
    try:
        from raya_core.whatsapp_handler import WhatsAppHandler
        
        handler = WhatsAppHandler()
        print("✅ WhatsApp handler created successfully")
        
        # Test driver setup
        if handler.setup_driver():
            print("✅ Chrome driver setup successful")
            handler.close()
            return True
        else:
            print("❌ Chrome driver setup failed")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please make sure all dependencies are installed.")
        return False
    except Exception as e:
        print(f"❌ Error testing WhatsApp connection: {e}")
        return False

def print_usage_instructions():
    """Print instructions for using WhatsApp features"""
    print("\n" + "="*60)
    print("🎉 WhatsApp Integration Setup Complete!")
    print("="*60)
    print("\n📋 How to use WhatsApp features with Raya:")
    print("\n1. 🔗 Connect to WhatsApp Web:")
    print("   Say: 'Setup WhatsApp' or 'Connect WhatsApp'")
    print("   - This will open WhatsApp Web in Chrome")
    print("   - Scan the QR code with your phone")
    print("   - Keep the browser window open")
    
    print("\n2. 📤 Send Messages:")
    print("   Say: 'Send WhatsApp message to [contact name] saying [your message]'")
    print("   Example: 'Send WhatsApp message to John saying Hello, how are you?'")
    
    print("\n3. 📨 Read Messages:")
    print("   Say: 'Read WhatsApp messages' or 'Check messages'")
    
    print("\n4. 👀 Monitor Messages:")
    print("   Say: 'Start monitoring WhatsApp' or 'Monitor WhatsApp'")
    print("   - Raya will watch for new messages")
    print("   - Can auto-generate AI responses")
    
    print("\n5. 🛑 Stop Monitoring:")
    print("   Say: 'Stop monitoring WhatsApp'")
    
    print("\n⚠️  Important Notes:")
    print("- Keep Chrome browser window open while using WhatsApp features")
    print("- First time setup requires QR code scan")
    print("- Session data is saved for future use")
    print("- Make sure your phone has internet connection")
    
    print("\n🚀 Ready to start! Run: python main.py")
    print("="*60)

def main():
    """Main setup function"""
    print("🤖 Raya AI Assistant - WhatsApp Integration Setup")
    print("="*50)
    
    # Step 1: Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies. Please check your internet connection and try again.")
        return False
    
    # Step 2: Setup Chrome driver
    if not setup_chrome_driver():
        print("❌ Failed to setup Chrome WebDriver. Please install Google Chrome and try again.")
        return False
    
    # Step 3: Create session directory
    create_whatsapp_session_dir()
    
    # Step 4: Test connection
    if not test_whatsapp_connection():
        print("❌ WhatsApp connection test failed. Please check the error messages above.")
        return False
    
    # Step 5: Print usage instructions
    print_usage_instructions()
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Setup failed. Please resolve the errors above and try again.")
        sys.exit(1)
    else:
        print("\n✅ Setup completed successfully!")
