from raya_core import listener, speaker, brain

def main():
    speaker.speak("Hello, I am AI Assistant. How can I help you?")
    try:
        while True:
            command = listener.listen()
            if command:
                if "stop" in command or "bye" in command:
                    speaker.speak("Goodbye! Have a nice day.")
                    break
                response = brain.process(command)
                speaker.speak(response)
    except KeyboardInterrupt:
        speaker.speak("Stopping. Goodbye!")

if __name__ == "__main__":
    main()
