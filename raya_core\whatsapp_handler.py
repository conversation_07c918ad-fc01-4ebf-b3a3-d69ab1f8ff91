import pywhatkit as pwk
import time
import re
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.options import Options
import threading
import os

class WhatsAppHandler:
    def __init__(self):
        self.driver = None
        self.is_logged_in = False
        self.monitoring = False
        self.last_message_time = None
        
    def setup_driver(self):
        """Setup Chrome driver for WhatsApp Web"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--user-data-dir=./whatsapp_session")  # Save session
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            return True
        except Exception as e:
            print(f"❌ Error setting up WhatsApp driver: {e}")
            return False
    
    def login_whatsapp_web(self):
        """Open WhatsApp Web and wait for QR code scan"""
        if not self.setup_driver():
            return False
            
        try:
            print("🌐 Opening WhatsApp Web...")
            self.driver.get("https://web.whatsapp.com")
            
            # Wait for either QR code or chat interface
            print("📱 Please scan the QR code with your phone...")
            
            # Wait up to 60 seconds for login
            wait = WebDriverWait(self.driver, 60)
            
            # Check if already logged in or wait for login
            try:
                # Wait for the main chat interface to load
                wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='chat-list']")))
                print("✅ Successfully logged into WhatsApp Web!")
                self.is_logged_in = True
                return True
            except:
                print("⏰ Login timeout. Please try again.")
                return False
                
        except Exception as e:
            print(f"❌ Error logging into WhatsApp Web: {e}")
            return False
    
    def send_message_simple(self, phone_number, message):
        """Send message using pywhatkit (simpler but less reliable)"""
        try:
            # Format phone number (add country code if not present)
            if not phone_number.startswith('+'):
                phone_number = '+' + phone_number
            
            print(f"📤 Sending message to {phone_number}: {message}")
            
            # Send message instantly (opens WhatsApp Web)
            pwk.sendwhatmsg_instantly(phone_number, message, wait_time=15, tab_close=True)
            
            return True
        except Exception as e:
            print(f"❌ Error sending message: {e}")
            return False
    
    def send_message_advanced(self, contact_name, message):
        """Send message using Selenium (more reliable)"""
        if not self.is_logged_in:
            print("❌ Please login to WhatsApp Web first!")
            return False
            
        try:
            # Search for contact
            search_box = self.driver.find_element(By.CSS_SELECTOR, "[data-testid='chat-list-search']")
            search_box.clear()
            search_box.send_keys(contact_name)
            time.sleep(2)
            
            # Click on first contact result
            contact = self.driver.find_element(By.CSS_SELECTOR, "[data-testid='cell-frame-container']")
            contact.click()
            time.sleep(1)
            
            # Type message
            message_box = self.driver.find_element(By.CSS_SELECTOR, "[data-testid='conversation-compose-box-input']")
            message_box.send_keys(message)
            
            # Send message
            send_button = self.driver.find_element(By.CSS_SELECTOR, "[data-testid='send']")
            send_button.click()
            
            print(f"✅ Message sent to {contact_name}: {message}")
            return True
            
        except Exception as e:
            print(f"❌ Error sending message to {contact_name}: {e}")
            return False
    
    def get_latest_messages(self, limit=5):
        """Get latest messages from current chat"""
        if not self.is_logged_in:
            return []
            
        try:
            # Get message elements
            messages = self.driver.find_elements(By.CSS_SELECTOR, "[data-testid='msg-container']")
            
            latest_messages = []
            for msg in messages[-limit:]:
                try:
                    # Get message text
                    text_element = msg.find_element(By.CSS_SELECTOR, ".selectable-text")
                    text = text_element.text
                    
                    # Get timestamp
                    time_element = msg.find_element(By.CSS_SELECTOR, "[data-testid='msg-meta']")
                    timestamp = time_element.text
                    
                    # Check if incoming or outgoing
                    is_incoming = "message-in" in msg.get_attribute("class")
                    
                    latest_messages.append({
                        'text': text,
                        'timestamp': timestamp,
                        'incoming': is_incoming
                    })
                except:
                    continue
                    
            return latest_messages
            
        except Exception as e:
            print(f"❌ Error getting messages: {e}")
            return []
    
    def monitor_messages(self, callback_function):
        """Monitor for new incoming messages"""
        if not self.is_logged_in:
            print("❌ Please login to WhatsApp Web first!")
            return
            
        self.monitoring = True
        print("👀 Starting message monitoring...")
        
        def monitor_loop():
            while self.monitoring:
                try:
                    # Check for unread message indicators
                    unread_chats = self.driver.find_elements(By.CSS_SELECTOR, "[data-testid='unread-count']")
                    
                    if unread_chats:
                        print("📨 New message detected!")
                        # Get the chat with unread messages
                        for unread in unread_chats:
                            chat_container = unread.find_element(By.XPATH, "./ancestor::div[@data-testid='cell-frame-container']")
                            chat_container.click()
                            time.sleep(1)
                            
                            # Get latest message
                            latest_messages = self.get_latest_messages(1)
                            if latest_messages and latest_messages[0]['incoming']:
                                callback_function(latest_messages[0])
                            
                            break
                    
                    time.sleep(3)  # Check every 3 seconds
                    
                except Exception as e:
                    print(f"❌ Error in message monitoring: {e}")
                    time.sleep(5)
        
        # Start monitoring in separate thread
        monitor_thread = threading.Thread(target=monitor_loop)
        monitor_thread.daemon = True
        monitor_thread.start()
    
    def stop_monitoring(self):
        """Stop message monitoring"""
        self.monitoring = False
        print("🛑 Stopped message monitoring")
    
    def close(self):
        """Close WhatsApp Web driver"""
        if self.driver:
            self.driver.quit()
            print("🔒 WhatsApp Web session closed")

# Global instance
whatsapp_handler = WhatsAppHandler()

def send_whatsapp_message(contact, message):
    """Simple function to send WhatsApp message"""
    return whatsapp_handler.send_message_simple(contact, message)

def setup_whatsapp():
    """Setup WhatsApp Web connection"""
    return whatsapp_handler.login_whatsapp_web()

def start_message_monitoring(callback):
    """Start monitoring WhatsApp messages"""
    whatsapp_handler.monitor_messages(callback)

def stop_message_monitoring():
    """Stop monitoring WhatsApp messages"""
    whatsapp_handler.stop_monitoring()
