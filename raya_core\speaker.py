import pyttsx3

def speak(text):
    """
    Convert text to speech (re-initialize pyttsx3 each time to avoid freeze)
    """
    try:
        print(f"Raya 🗣️: {text}")

        # Initialize TTS engine
        engine = pyttsx3.init()
        engine.setProperty('rate', 130)  # Speed
        engine.setProperty('volume', 1.0)  # Volume max

        # Try to pick a female voice if available
        voices = engine.getProperty('voices')
        for voice in voices:
            if "female" in voice.name.lower() or "zira" in voice.name.lower():
                engine.setProperty('voice', voice.id)
                break

        engine.say(text)
        engine.runAndWait()

        # Clean up engine to avoid freezing
        engine.stop()

    except Exception as e:
        print(f"❌ TTS Error: {e}")
