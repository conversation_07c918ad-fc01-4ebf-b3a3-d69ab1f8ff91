#!/usr/bin/env python3
"""
Test script for simple WhatsApp integration
This script tests the WhatsApp functionality without complex setup
"""

import sys
import os

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        import pywhatkit as pwk
        print("✅ pywhatkit imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import pywhatkit: {e}")
        return False
    
    try:
        from raya_core.whatsapp_simple import SimpleWhatsAppHandler
        print("✅ SimpleWhatsAppHandler imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import SimpleWhatsAppHandler: {e}")
        return False
    
    try:
        from raya_core import brain
        print("✅ Brain module imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import brain module: {e}")
        return False
    
    return True

def test_whatsapp_handler():
    """Test WhatsApp handler functionality"""
    print("\n🧪 Testing WhatsApp handler...")
    
    try:
        from raya_core.whatsapp_simple import SimpleWhatsAppHandler
        
        handler = SimpleWhatsAppHandler()
        print("✅ WhatsApp handler created")
        
        # Test info retrieval
        info = handler.get_whatsapp_info()
        print("✅ WhatsApp info retrieved:")
        print(f"   Features: {len(info['features'])} available")
        print(f"   Requirements: {len(info['requirements'])} items")
        print(f"   Limitations: {len(info['limitations'])} noted")
        
        return True
        
    except Exception as e:
        print(f"❌ WhatsApp handler test failed: {e}")
        return False

def test_brain_integration():
    """Test brain integration with WhatsApp commands"""
    print("\n🧪 Testing brain integration...")
    
    try:
        from raya_core import brain
        
        # Test WhatsApp commands
        test_commands = [
            "setup whatsapp",
            "send whatsapp message to +1234567890 saying test message",
            "open whatsapp chat with +1234567890",
            "read whatsapp messages",
            "start monitoring whatsapp"
        ]
        
        for command in test_commands:
            print(f"   Testing: '{command}'")
            try:
                response = brain.process(command)
                print(f"   ✅ Response: {response[:50]}...")
            except Exception as e:
                print(f"   ❌ Error: {e}")
                return False
        
        print("✅ Brain integration test completed")
        return True
        
    except Exception as e:
        print(f"❌ Brain integration test failed: {e}")
        return False

def test_phone_number_parsing():
    """Test phone number parsing functionality"""
    print("\n🧪 Testing phone number parsing...")
    
    try:
        from raya_core.whatsapp_simple import parse_phone_number
        
        test_numbers = [
            "+1234567890",
            "************", 
            "(*************",
            "************",
            "+44 20 7946 0958"
        ]
        
        for number in test_numbers:
            parsed = parse_phone_number(number)
            print(f"   '{number}' -> '{parsed}'")
        
        print("✅ Phone number parsing test completed")
        return True
        
    except Exception as e:
        print(f"❌ Phone number parsing test failed: {e}")
        return False

def test_message_parsing():
    """Test message parsing functionality"""
    print("\n🧪 Testing message parsing...")
    
    try:
        from raya_core.whatsapp_simple import parse_contact_name_and_message
        
        test_queries = [
            "send whatsapp message to John saying hello how are you",
            "send message to +1234567890 saying test message",
            "send whatsapp to Mom saying I'll be late"
        ]
        
        for query in test_queries:
            contact, message = parse_contact_name_and_message(query)
            print(f"   '{query}'")
            print(f"   -> Contact: '{contact}', Message: '{message}'")
        
        print("✅ Message parsing test completed")
        return True
        
    except Exception as e:
        print(f"❌ Message parsing test failed: {e}")
        return False

def interactive_test():
    """Interactive test for WhatsApp functionality"""
    print("\n🎮 Interactive WhatsApp Test")
    print("-" * 40)
    
    print("This test will demonstrate WhatsApp functionality.")
    print("⚠️  WARNING: This will actually try to send messages!")
    
    proceed = input("Do you want to proceed with interactive test? (y/n): ").lower().strip()
    
    if proceed != 'y':
        print("🚫 Interactive test skipped")
        return True
    
    try:
        from raya_core.whatsapp_simple import send_whatsapp_simple
        
        # Get test phone number
        phone = input("Enter a test phone number (with country code, e.g., +1234567890): ").strip()
        if not phone:
            print("🚫 No phone number provided, skipping interactive test")
            return True
        
        # Get test message
        message = input("Enter a test message: ").strip()
        if not message:
            message = "Test message from Raya AI Assistant"
        
        print(f"\n📤 Attempting to send message...")
        print(f"   To: {phone}")
        print(f"   Message: {message}")
        
        confirm = input("Confirm sending this message? (y/n): ").lower().strip()
        if confirm == 'y':
            success = send_whatsapp_simple(phone, message)
            if success:
                print("✅ Interactive test completed successfully!")
                print("   Check your WhatsApp to see if the message was sent.")
            else:
                print("❌ Interactive test failed")
                return False
        else:
            print("🚫 Message sending cancelled")
        
        return True
        
    except Exception as e:
        print(f"❌ Interactive test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🤖 Raya AI Assistant - Simple WhatsApp Test")
    print("=" * 45)
    
    tests = [
        ("Import Test", test_imports),
        ("WhatsApp Handler Test", test_whatsapp_handler),
        ("Brain Integration Test", test_brain_integration),
        ("Phone Number Parsing Test", test_phone_number_parsing),
        ("Message Parsing Test", test_message_parsing)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! WhatsApp integration is working correctly.")
        
        # Offer interactive test
        interactive_test()
        
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("\n💡 Common solutions:")
        print("   • Run: pip install pywhatkit pyautogui pillow")
        print("   • Make sure you're in the correct directory")
        print("   • Check that all files are in place")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
    else:
        print("\n✅ WhatsApp integration test completed successfully!")
