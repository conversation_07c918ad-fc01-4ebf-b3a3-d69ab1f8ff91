#!/usr/bin/env python3
"""
Quick test script for your WhatsApp integration
"""

from raya_core import brain

def test_whatsapp_with_real_number():
    """Test WhatsApp with your actual phone number"""
    print("🤖 Testing WhatsApp Integration")
    print("=" * 40)
    
    # Get your phone number
    phone = input("Enter your phone number (with country code, e.g., +1234567890): ").strip()
    
    if not phone:
        print("❌ No phone number provided")
        return
    
    if not phone.startswith('+'):
        print("⚠️  Adding + to phone number")
        phone = '+' + phone
    
    # Test message
    message = "Hello! This is a test message from my Raya AI assistant. It's working perfectly! 🤖"
    
    print(f"\n📤 Testing WhatsApp message:")
    print(f"   To: {phone}")
    print(f"   Message: {message}")
    
    confirm = input("\nSend this test message to yourself? (y/n): ").lower().strip()
    
    if confirm == 'y':
        # Create the voice command
        command = f"send whatsapp message to {phone} saying {message}"
        
        print(f"\n🎤 Simulating voice command: '{command}'")
        print("-" * 50)
        
        # Process the command
        response = brain.process(command)
        
        print(f"🗣️ Raya Response: {response}")
        print("\n✅ Check your WhatsApp to see if the message was sent!")
        
    else:
        print("🚫 Test cancelled")

def test_whatsapp_group():
    """Test WhatsApp group messaging"""
    print("\n📱 Testing Group Message")
    print("=" * 30)
    
    group_name = input("Enter a WhatsApp group name (exact name): ").strip()
    
    if not group_name:
        print("❌ No group name provided")
        return
    
    message = "Hello everyone! This is a test message from my Raya AI assistant. 🤖"
    
    print(f"\n📤 Testing group message:")
    print(f"   Group: {group_name}")
    print(f"   Message: {message}")
    
    confirm = input("\nSend this test message to the group? (y/n): ").lower().strip()
    
    if confirm == 'y':
        command = f"send whatsapp group message to {group_name} saying {message}"
        
        print(f"\n🎤 Simulating voice command: '{command}'")
        print("-" * 50)
        
        response = brain.process(command)
        
        print(f"🗣️ Raya Response: {response}")
        print("\n✅ Check your WhatsApp to see if the group message was sent!")
        
    else:
        print("🚫 Test cancelled")

def main():
    """Main test function"""
    print("🚀 WhatsApp Integration Test")
    print("This will test your actual WhatsApp integration")
    print("=" * 50)
    
    while True:
        print("\nOptions:")
        print("1. Test message to yourself")
        print("2. Test group message")
        print("3. Test setup command")
        print("q. Quit")
        
        choice = input("\nEnter your choice: ").strip().lower()
        
        if choice == 'q':
            print("👋 Test completed!")
            break
        elif choice == '1':
            test_whatsapp_with_real_number()
        elif choice == '2':
            test_whatsapp_group()
        elif choice == '3':
            print("\n🔧 Testing setup command...")
            response = brain.process("setup whatsapp")
            print(f"🗣️ Raya Response: {response}")
        else:
            print("❌ Invalid choice")

if __name__ == "__main__":
    main()
