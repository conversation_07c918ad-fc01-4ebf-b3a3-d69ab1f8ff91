# <PERSON>a Voice Assistant

A voice-controlled AI assistant with WhatsApp integration that can perform tasks, answer questions using OpenAI's GPT, and manage WhatsApp messages.

## Features

- 🎤 Voice recognition and text-to-speech capabilities
- 🌐 Built-in commands for opening YouTube and telling the time
- 🤖 Integration with OpenAI's GPT for answering general questions
- 💬 **WhatsApp Integration** - Send, receive, and monitor WhatsApp messages
- 🔄 Natural conversation flow
- 👀 Automatic message monitoring with AI-generated responses

## Requirements

- Python 3.6+
- Google Chrome browser (for WhatsApp Web integration)
- WhatsApp account with phone access for QR code scanning
- See `requirements.txt` for dependencies

## Installation

### Basic Setup
1. Clone this repository
2. Install dependencies: `pip install -r requirements.txt`
3. Create a `.env` file with your OpenAI API key (optional)

### WhatsApp Integration Setup
1. Run the WhatsApp setup script: `python setup_whatsapp.py`
2. Follow the on-screen instructions
3. The script will install additional dependencies and set up Chrome WebDriver

## Usage

### Basic Usage
Run the assistant:
```bash
python main.py
```

### WhatsApp Demo
Try the WhatsApp features:
```bash
python whatsapp_example.py
```

## Voice Commands

### Basic Commands
- "Open YouTube" - Opens YouTube in your default browser
- "What time is it?" - Tells the current time
- "Stop" or "Bye" - Exits the assistant

### WhatsApp Commands
- "Setup WhatsApp" or "Connect WhatsApp" - Connect to WhatsApp Web
- "Send WhatsApp message to [contact] saying [message]" - Send a message
- "Read WhatsApp messages" or "Check messages" - Read recent messages
- "Start monitoring WhatsApp" - Begin monitoring for new messages
- "Stop monitoring WhatsApp" - Stop message monitoring

## WhatsApp Integration Details

### How It Works
1. **WhatsApp Web Automation**: Uses Selenium to control WhatsApp Web in Chrome
2. **Message Sending**: Can send messages to contacts by name or phone number
3. **Message Reading**: Reads recent messages from active chats
4. **Auto-Monitoring**: Watches for new incoming messages
5. **AI Responses**: Can generate AI responses to incoming messages

### Setup Process
1. Run `python setup_whatsapp.py` to install dependencies
2. Say "Setup WhatsApp" to Raya
3. Chrome will open WhatsApp Web
4. Scan QR code with your phone
5. Keep Chrome window open while using WhatsApp features

### Security Notes
- Session data is saved locally for convenience
- No messages are stored permanently
- WhatsApp Web session follows WhatsApp's security protocols
- AI responses are generated locally using your OpenAI API key

## Examples

### Send a Message
```
You: "Send WhatsApp message to John saying Hello, how are you today?"
Raya: "Message sent to John: Hello, how are you today?"
```

### Monitor Messages
```
You: "Start monitoring WhatsApp"
Raya: "Started monitoring WhatsApp messages. I'll notify you of new messages."
[When new message arrives]
Raya: "New WhatsApp message from Sarah: Are we still meeting today?"
Raya: "Suggested AI response: Yes, we're still on for 3 PM. See you then!"
```

## Troubleshooting

### Common Issues
1. **Chrome not found**: Install Google Chrome browser
2. **QR code expired**: Refresh WhatsApp Web and scan again
3. **Messages not sending**: Check contact name spelling
4. **Monitoring not working**: Ensure Chrome window stays open

### Dependencies Issues
If you encounter dependency issues, try:
```bash
pip install --upgrade pip
pip install -r requirements.txt --force-reinstall
```

## Contributing

Feel free to contribute by:
- Adding new voice commands
- Improving WhatsApp integration
- Adding support for other messaging platforms
- Enhancing AI response generation