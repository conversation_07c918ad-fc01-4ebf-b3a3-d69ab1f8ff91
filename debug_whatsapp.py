#!/usr/bin/env python3
"""
Debug script to test WhatsApp command recognition
"""

from raya_core import brain

def test_command_recognition():
    """Test various WhatsApp command variations"""
    print("🔍 Testing WhatsApp Command Recognition")
    print("=" * 50)
    
    test_commands = [
        # Setup commands
        "setup whatsapp",
        "connect whatsapp", 
        "can you setup whatsapp",
        
        # Send message commands
        "send whatsapp message to +1234567890 saying hello",
        "send message to +1234567890 saying hello",
        "can you send whatsapp message to john saying hi",
        
        # Open chat commands
        "open whatsapp chat with +1234567890",
        "can you open my whatsapp chat",
        "open whatsapp",
        "whatsapp chat with john",
        
        # Group commands
        "send whatsapp group message to family saying hello",
        "send group message to work saying meeting",
        
        # Read commands
        "read whatsapp messages",
        "check my whatsapp messages",
        "whatsapp messages",
        
        # Monitor commands
        "start monitoring whatsapp",
        "monitor whatsapp messages",
        
        # Non-WhatsApp commands (should go to GPT)
        "what is the weather today",
        "tell me a joke"
    ]
    
    for i, command in enumerate(test_commands, 1):
        print(f"\n{i:2d}. Testing: '{command}'")
        print("-" * 40)
        
        try:
            response = brain.process(command)
            print(f"    Response: {response[:100]}...")
            
            # Check if it's a WhatsApp response
            if any(keyword in response.lower() for keyword in ['whatsapp', 'message sent', 'chat opened', 'monitoring']):
                print("    ✅ WhatsApp command recognized")
            elif "sorry, i couldn't contact my brain" in response.lower():
                print("    🤖 Sent to AI (no API key)")
            else:
                print("    🤖 Sent to AI")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")

def test_specific_issue():
    """Test the specific command that wasn't working"""
    print("\n" + "=" * 50)
    print("🎯 Testing Specific Issue")
    print("=" * 50)
    
    problematic_commands = [
        "can you open my whatsapp chat",
        "open my whatsapp",
        "whatsapp chat",
        "open whatsapp chat with +1234567890"
    ]
    
    for command in problematic_commands:
        print(f"\n🎤 Command: '{command}'")
        response = brain.process(command)
        print(f"🗣️  Response: {response}")

def main():
    """Main debug function"""
    print("🐛 WhatsApp Integration Debug Tool")
    print("This will help identify why WhatsApp commands aren't working")
    print("=" * 60)
    
    choice = input("\nChoose test:\n1. Full command recognition test\n2. Specific issue test\n3. Both\nEnter choice (1-3): ").strip()
    
    if choice in ['1', '3']:
        test_command_recognition()
    
    if choice in ['2', '3']:
        test_specific_issue()
    
    print("\n" + "=" * 60)
    print("🔧 Debug completed!")
    print("\nIf WhatsApp commands still aren't working:")
    print("1. Check that raya_core/whatsapp_simple.py exists")
    print("2. Verify all imports are working")
    print("3. Try the exact command format: 'send whatsapp message to +1234567890 saying hello'")

if __name__ == "__main__":
    main()
