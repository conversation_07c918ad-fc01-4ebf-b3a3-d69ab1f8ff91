"""
Simple WhatsApp Integration for Raya AI Assistant
This module provides WhatsApp functionality using pywhatkit (no Selenium required)
"""

import pywhatkit as pwk
import time
import re
from datetime import datetime, timedelta
import webbrowser
import pyautogui
import os

class SimpleWhatsAppHandler:
    def __init__(self):
        self.last_message_time = None
        
    def send_message_now(self, phone_number, message):
        """Send WhatsApp message immediately using pywhatkit"""
        try:
            # Format phone number (add country code if not present)
            if not phone_number.startswith('+'):
                # Assume US number if no country code
                phone_number = '+1' + phone_number.replace('-', '').replace(' ', '')
            
            print(f"📤 Sending WhatsApp message to {phone_number}")
            print(f"💬 Message: {message}")
            
            # Send message instantly (opens WhatsApp Web)
            pwk.sendwhatmsg_instantly(
                phone_number, 
                message, 
                wait_time=10,  # Wait 10 seconds for WhatsApp to load
                tab_close=True,  # Close tab after sending
                close_time=3     # Wait 3 seconds before closing
            )
            
            print("✅ Message sent successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Error sending message: {e}")
            return False
    
    def send_message_scheduled(self, phone_number, message, hour, minute):
        """Send WhatsApp message at scheduled time"""
        try:
            if not phone_number.startswith('+'):
                phone_number = '+1' + phone_number.replace('-', '').replace(' ', '')
            
            print(f"📅 Scheduling WhatsApp message to {phone_number} at {hour}:{minute:02d}")
            print(f"💬 Message: {message}")
            
            pwk.sendwhatmsg(phone_number, message, hour, minute)
            
            print("✅ Message scheduled successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Error scheduling message: {e}")
            return False
    
    def send_to_group(self, group_name, message):
        """Send message to WhatsApp group"""
        try:
            print(f"📤 Sending message to group: {group_name}")
            print(f"💬 Message: {message}")
            
            pwk.sendwhatmsg_to_group_instantly(
                group_name,
                message,
                wait_time=10,
                tab_close=True,
                close_time=3
            )
            
            print("✅ Group message sent successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Error sending group message: {e}")
            return False
    
    def open_whatsapp_chat(self, phone_number):
        """Open WhatsApp chat with specific contact"""
        try:
            if not phone_number.startswith('+'):
                phone_number = '+1' + phone_number.replace('-', '').replace(' ', '')
            
            # Open WhatsApp Web with specific contact
            url = f"https://web.whatsapp.com/send?phone={phone_number.replace('+', '')}"
            webbrowser.open(url)
            
            print(f"🌐 Opened WhatsApp chat with {phone_number}")
            return True
            
        except Exception as e:
            print(f"❌ Error opening WhatsApp chat: {e}")
            return False
    
    def send_image(self, phone_number, image_path, caption=""):
        """Send image via WhatsApp"""
        try:
            if not os.path.exists(image_path):
                print(f"❌ Image file not found: {image_path}")
                return False
            
            if not phone_number.startswith('+'):
                phone_number = '+1' + phone_number.replace('-', '').replace(' ', '')
            
            print(f"📸 Sending image to {phone_number}")
            print(f"🖼️ Image: {image_path}")
            if caption:
                print(f"💬 Caption: {caption}")
            
            pwk.sendwhats_image(
                phone_number,
                image_path,
                caption,
                wait_time=10,
                tab_close=True,
                close_time=3
            )
            
            print("✅ Image sent successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Error sending image: {e}")
            return False
    
    def get_whatsapp_info(self):
        """Get information about WhatsApp functionality"""
        info = {
            "features": [
                "Send instant messages",
                "Schedule messages",
                "Send to groups",
                "Send images with captions",
                "Open specific chats"
            ],
            "requirements": [
                "WhatsApp account",
                "Internet connection", 
                "Default web browser"
            ],
            "limitations": [
                "Cannot read incoming messages",
                "Cannot monitor messages automatically",
                "Requires browser interaction",
                "No real-time message detection"
            ]
        }
        return info

# Global instance
simple_whatsapp = SimpleWhatsAppHandler()

def send_whatsapp_simple(phone_number, message):
    """Simple function to send WhatsApp message"""
    return simple_whatsapp.send_message_now(phone_number, message)

def send_whatsapp_scheduled(phone_number, message, hour, minute):
    """Send scheduled WhatsApp message"""
    return simple_whatsapp.send_message_scheduled(phone_number, message, hour, minute)

def send_whatsapp_group(group_name, message):
    """Send message to WhatsApp group"""
    return simple_whatsapp.send_to_group(group_name, message)

def open_whatsapp_chat(phone_number):
    """Open WhatsApp chat"""
    return simple_whatsapp.open_whatsapp_chat(phone_number)

def send_whatsapp_image(phone_number, image_path, caption=""):
    """Send image via WhatsApp"""
    return simple_whatsapp.send_image(phone_number, image_path, caption)

def parse_phone_number(text):
    """Extract phone number from text"""
    # Look for phone number patterns
    patterns = [
        r'\+?1?[-.\s]?\(?(\d{3})\)?[-.\s]?(\d{3})[-.\s]?(\d{4})',  # US format
        r'\+(\d{1,3})[-.\s]?(\d{3,4})[-.\s]?(\d{3,4})[-.\s]?(\d{3,4})',  # International
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text)
        if match:
            if pattern == patterns[0]:  # US format
                return f"+1{match.group(1)}{match.group(2)}{match.group(3)}"
            else:  # International
                return f"+{match.group(1)}{match.group(2)}{match.group(3)}{match.group(4)}"
    
    return None

def parse_contact_name_and_message(query):
    """Parse contact name and message from voice command"""
    # Pattern: "send whatsapp message to [contact] saying [message]"
    pattern = r"send.*(?:whatsapp|message).*to\s+([^s]+)\s+saying\s+(.+)"
    match = re.search(pattern, query.lower())
    
    if match:
        contact = match.group(1).strip()
        message = match.group(2).strip()
        return contact, message
    
    return None, None

def format_whatsapp_response(success, contact, message, error=None):
    """Format response for WhatsApp operations"""
    if success:
        return f"✅ WhatsApp message sent to {contact}: '{message}'"
    else:
        error_msg = f" Error: {error}" if error else ""
        return f"❌ Failed to send WhatsApp message to {contact}.{error_msg}"
