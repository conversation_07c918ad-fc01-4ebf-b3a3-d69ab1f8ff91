import openai
import os
import re
from dotenv import load_dotenv
from raya_core import wakeword as commands
from raya_core import whatsapp_simple

# Load API key from .env file
load_dotenv()
openai.api_key = os.getenv("OPENAI_API_KEY")

def process(query):
    """
    Process user query: check commands or send to OpenAI
    """
    query_lower = query.lower()

    # Basic commands
    if "open youtube" in query_lower:
        return commands.open_youtube()
    elif "what time" in query_lower:
        return commands.tell_time()
    elif "stop" in query_lower or "bye" in query_lower:
        return "Stopping. Goodbye!"

    # WhatsApp commands
    elif "setup whatsapp" in query_lower or "connect whatsapp" in query_lower:
        return setup_whatsapp_connection()
    elif "send whatsapp" in query_lower and "group" in query_lower:
        return handle_send_whatsapp_group(query)
    elif "send whatsapp" in query_lower or "send message" in query_lower:
        return handle_send_whatsapp_message(query)
    elif "open whatsapp chat" in query_lower:
        return handle_open_whatsapp_chat(query)
    elif "read whatsapp" in query_lower or "check messages" in query_lower:
        return handle_read_whatsapp_messages()
    elif "start monitoring" in query_lower or "monitor whatsapp" in query_lower:
        return start_whatsapp_monitoring()
    elif "stop monitoring" in query_lower:
        return stop_whatsapp_monitoring()

    # Default to GPT
    else:
        return ask_gpt(query)

def ask_gpt(prompt):
    """
    Send user prompt to OpenAI ChatGPT API
    """
    try:
        response = openai.ChatCompletion.create(
            model="gpt-4",  # Or gpt-4 if you have access
            messages=[
                {"role": "system", "content": "You are a friendly AI bot. Reply to:"},
                {"role": "user", "content": prompt}
            ]
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"OpenAI API Error: {e}")  # Debug print
        return "Sorry, I couldn’t contact my brain."

# WhatsApp handling functions
def setup_whatsapp_connection():
    """Setup WhatsApp connection (simple mode)"""
    try:
        info = whatsapp_simple.simple_whatsapp.get_whatsapp_info()
        return "WhatsApp is ready! You can send messages using voice commands. Note: This uses simple mode - messages will open in your browser."
    except Exception as e:
        return f"Error setting up WhatsApp: {e}"

def handle_send_whatsapp_message(query):
    """Parse and send WhatsApp message from voice command"""
    try:
        # Extract contact and message from query
        contact, message = whatsapp_simple.parse_contact_name_and_message(query)

        if contact and message:
            # Try to parse as phone number first
            phone_number = whatsapp_simple.parse_phone_number(contact)

            if phone_number:
                # Send to phone number
                success = whatsapp_simple.send_whatsapp_simple(phone_number, message)
                return whatsapp_simple.format_whatsapp_response(success, phone_number, message)
            else:
                # Treat as contact name - convert to a phone number format
                # For demo purposes, you'd need to maintain a contact list
                return f"Please provide a phone number for {contact}. Say: 'Send WhatsApp message to +1234567890 saying {message}'"
        else:
            return "Please say: 'Send WhatsApp message to [phone number] saying [your message]'. Example: 'Send WhatsApp message to +1234567890 saying Hello!'"

    except Exception as e:
        return f"Error sending WhatsApp message: {e}"

def handle_read_whatsapp_messages():
    """Read latest WhatsApp messages (not available in simple mode)"""
    return "Reading messages is not available in simple mode. This feature requires the advanced WhatsApp Web integration."

def start_whatsapp_monitoring():
    """Start monitoring WhatsApp messages (not available in simple mode)"""
    return "Message monitoring is not available in simple mode. You can send messages, but automatic monitoring requires the advanced integration."

def stop_whatsapp_monitoring():
    """Stop monitoring WhatsApp messages"""
    return "Message monitoring is not active in simple mode."

def handle_send_whatsapp_group(query):
    """Send message to WhatsApp group"""
    try:
        # Pattern: "send whatsapp group message to [group] saying [message]"
        pattern = r"send.*(?:whatsapp|group).*(?:group|to)\s+([^s]+)\s+saying\s+(.+)"
        match = re.search(pattern, query.lower())

        if match:
            group_name = match.group(1).strip()
            message = match.group(2).strip()

            success = whatsapp_simple.send_whatsapp_group(group_name, message)
            if success:
                return f"✅ Group message sent to '{group_name}': {message}"
            else:
                return f"❌ Failed to send group message to '{group_name}'"
        else:
            return "Please say: 'Send WhatsApp group message to [group name] saying [your message]'"

    except Exception as e:
        return f"Error sending group message: {e}"

def handle_open_whatsapp_chat(query):
    """Open WhatsApp chat with contact"""
    try:
        # Pattern: "open whatsapp chat with [contact/number]"
        pattern = r"open.*whatsapp.*chat.*with\s+(.+)"
        match = re.search(pattern, query.lower())

        if match:
            contact = match.group(1).strip()
            phone_number = whatsapp_simple.parse_phone_number(contact)

            if phone_number:
                success = whatsapp_simple.open_whatsapp_chat(phone_number)
                if success:
                    return f"✅ Opened WhatsApp chat with {phone_number}"
                else:
                    return f"❌ Failed to open chat with {phone_number}"
            else:
                return f"Please provide a valid phone number. Example: 'Open WhatsApp chat with +1234567890'"
        else:
            return "Please say: 'Open WhatsApp chat with [phone number]'"

    except Exception as e:
        return f"Error opening WhatsApp chat: {e}"
