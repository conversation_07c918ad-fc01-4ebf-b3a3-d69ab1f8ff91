import openai
import os
import re
from dotenv import load_dotenv
from raya_core import wakeword as commands
from raya_core import whatsapp_handler

# Load API key from .env file
load_dotenv()
openai.api_key = os.getenv("OPENAI_API_KEY")

def process(query):
    """
    Process user query: check commands or send to OpenAI
    """
    query_lower = query.lower()

    # Basic commands
    if "open youtube" in query_lower:
        return commands.open_youtube()
    elif "what time" in query_lower:
        return commands.tell_time()
    elif "stop" in query_lower or "bye" in query_lower:
        return "Stopping. Goodbye!"

    # WhatsApp commands
    elif "setup whatsapp" in query_lower or "connect whatsapp" in query_lower:
        return setup_whatsapp_connection()
    elif "send whatsapp" in query_lower or "send message" in query_lower:
        return handle_send_whatsapp_message(query)
    elif "read whatsapp" in query_lower or "check messages" in query_lower:
        return handle_read_whatsapp_messages()
    elif "start monitoring" in query_lower or "monitor whatsapp" in query_lower:
        return start_whatsapp_monitoring()
    elif "stop monitoring" in query_lower:
        return stop_whatsapp_monitoring()

    # Default to GPT
    else:
        return ask_gpt(query)

def ask_gpt(prompt):
    """
    Send user prompt to OpenAI ChatGPT API
    """
    try:
        response = openai.ChatCompletion.create(
            model="gpt-4",  # Or gpt-4 if you have access
            messages=[
                {"role": "system", "content": "You are a friendly AI bot. Reply to:"},
                {"role": "user", "content": prompt}
            ]
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"OpenAI API Error: {e}")  # Debug print
        return "Sorry, I couldn’t contact my brain."

# WhatsApp handling functions
def setup_whatsapp_connection():
    """Setup WhatsApp Web connection"""
    try:
        success = whatsapp_handler.setup_whatsapp()
        if success:
            return "WhatsApp Web is now connected! You can now send and receive messages."
        else:
            return "Failed to connect to WhatsApp Web. Please try again."
    except Exception as e:
        return f"Error setting up WhatsApp: {e}"

def handle_send_whatsapp_message(query):
    """Parse and send WhatsApp message from voice command"""
    try:
        # Extract contact and message from query
        # Example: "send whatsapp message to John saying hello how are you"

        # Pattern to extract contact and message
        pattern = r"send.*(?:whatsapp|message).*to\s+(\w+).*saying\s+(.+)"
        match = re.search(pattern, query.lower())

        if match:
            contact = match.group(1).title()
            message = match.group(2)

            success = whatsapp_handler.send_whatsapp_message(contact, message)
            if success:
                return f"Message sent to {contact}: {message}"
            else:
                return f"Failed to send message to {contact}"
        else:
            return "Please say: 'Send WhatsApp message to [contact name] saying [your message]'"

    except Exception as e:
        return f"Error sending WhatsApp message: {e}"

def handle_read_whatsapp_messages():
    """Read latest WhatsApp messages"""
    try:
        messages = whatsapp_handler.whatsapp_handler.get_latest_messages(3)
        if messages:
            response = "Here are your latest WhatsApp messages: "
            for i, msg in enumerate(messages, 1):
                sender = "You" if not msg['incoming'] else "Contact"
                response += f"Message {i}: {sender} said: {msg['text']}. "
            return response
        else:
            return "No recent messages found."
    except Exception as e:
        return f"Error reading WhatsApp messages: {e}"

def start_whatsapp_monitoring():
    """Start monitoring WhatsApp messages"""
    try:
        def message_callback(message):
            """Handle new incoming message"""
            print(f"📨 New WhatsApp message: {message['text']}")

            # Generate AI response
            ai_response = ask_gpt(f"Someone sent me this WhatsApp message: '{message['text']}'. Generate a brief, friendly response.")

            # You can choose to auto-reply or just notify
            print(f"🤖 Suggested response: {ai_response}")
            # Uncomment below to auto-reply:
            # whatsapp_handler.whatsapp_handler.send_message_advanced("Contact", ai_response)

        whatsapp_handler.start_message_monitoring(message_callback)
        return "Started monitoring WhatsApp messages. I'll notify you of new messages."
    except Exception as e:
        return f"Error starting WhatsApp monitoring: {e}"

def stop_whatsapp_monitoring():
    """Stop monitoring WhatsApp messages"""
    try:
        whatsapp_handler.stop_message_monitoring()
        return "Stopped monitoring WhatsApp messages."
    except Exception as e:
        return f"Error stopping WhatsApp monitoring: {e}"
